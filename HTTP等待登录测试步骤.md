# HTTP 等待登录测试步骤

## 🎯 测试目标
验证页面刷新后 HTTP 请求会等待登录完成，避免 401 错误。

## 🔧 测试准备
1. 确保已实现全局刷新重登功能
2. 打开浏览器开发者工具的 Console 面板
3. 打开 Network 面板监控请求

## 📋 核心测试场景

### 测试 1：基础等待机制
**步骤**：
1. 登录进入 `/home` 或其他有数据请求的页面
2. 按 F5 刷新页面
3. 观察 Console 日志

**预期日志顺序**：
```
1. Request waiting for login completion: /api/xxx
2. Detected page refresh, triggering global refresh relogin
3. Global refresh relogin triggered
4. <PERSON><PERSON> completed, releasing waiting requests
5. <PERSON><PERSON> completed, proceeding with request: /api/xxx
```

**验证点**：
- ✅ 请求被拦截等待
- ✅ 登录完成后请求继续
- ✅ Network 面板中请求成功（200状态）
- ✅ 没有 401 错误

### 测试 2：多请求同时等待
**步骤**：
1. 登录进入有多个 API 调用的页面（如设备管理页）
2. 刷新页面
3. 观察多个请求的处理

**预期结果**：
- 所有请求都显示等待日志
- 登录完成后所有请求同时释放
- 所有请求都成功执行

### 测试 3：超时机制
**步骤**：
1. 断开网络或模拟登录失败
2. 刷新页面
3. 等待 15 秒以上

**预期结果**：
```
Wait for login timeout
Wait for login failed: Error: Wait for login timeout
```

## 🚀 快速验证方法

### 方法 1：Console 日志验证
```javascript
// 在 Console 中执行，检查当前状态
console.log('localStorage token:', localStorage.getItem('Admin-Token'))
console.log('sessionStorage username:', sessionStorage.username)
console.log('store token:', this.$store.getters.token)
```

### 方法 2：手动触发等待
```javascript
// 在 Console 中手动清除 store token 来模拟刷新状态
this.$store.commit('SET_TOKEN', '')
// 然后发送任意请求，应该会被等待
```

## 🔍 关键日志标识

### ✅ 成功的等待流程：
```
Request waiting for login completion: [URL]
Login completed, releasing waiting requests
Login completed, proceeding with request: [URL]
```

### ❌ 超时情况：
```
Wait for login timeout
Wait for login failed: Error: Wait for login timeout
```

### ✅ 正常请求（无需等待）：
```
// 没有等待相关日志，请求直接执行
```

## 🛠️ 故障排除

### 问题：请求仍然返回 401
**检查项**：
1. 确认等待日志是否出现
2. 检查全局刷新重登是否成功
3. 验证 token 是否正确更新

### 问题：请求一直等待
**检查项**：
1. 确认登录是否成功完成
2. 检查 15 秒后是否有超时日志
3. 验证 store.getters.token 是否有值

### 问题：正常请求被误等待
**检查项**：
1. 确认 localStorage 和 sessionStorage 状态
2. 检查 Vuex store 中的 token 状态
3. 验证等待条件判断逻辑

## 📊 性能验证

### 正常状态下的性能：
- 请求不应该有额外延迟
- 没有等待相关的日志输出
- Network 面板显示正常的请求时间

### 等待状态下的性能：
- 等待时间应该在 1-3 秒内（正常登录速度）
- 超时时间为 15 秒
- 多个请求应该同时释放

## ✅ 验证清单

- [ ] 页面刷新后请求被正确等待
- [ ] 登录完成后请求正确释放  
- [ ] 多个请求同时等待和释放
- [ ] 15 秒超时机制正常工作
- [ ] 正常状态下请求不受影响
- [ ] Network 面板中没有 401 错误
- [ ] Console 日志符合预期
- [ ] 页面功能正常加载

## 🎉 成功标准

当所有测试场景都通过时，说明 HTTP 等待登录机制工作正常：

1. **功能性**：请求正确等待和释放
2. **可靠性**：超时和错误处理正常
3. **性能**：正常状态下无额外开销
4. **兼容性**：与现有功能完全兼容

完成测试后，用户在页面刷新时将享受无缝的体验，不再遇到 401 错误！
