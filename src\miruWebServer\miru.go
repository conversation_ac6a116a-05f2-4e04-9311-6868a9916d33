package main

import (
	"encoding/json"
	"fmt"
	"io"
	"miruWebServer/pub"
	"mirulib"
	"mirulib/worker"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

const videoDir string = "/video"

func getIncrementItem(olds, news []string) ([]string, []string) {
	var adds = []string{}
	var dels = []string{}
	var oldMap = make(map[string]int)
	for _, old := range olds {
		oldMap[old] = 1
	}
	for _, new := range news {
		_, ok := oldMap[new]
		if ok {
			delete(oldMap, new)
		} else {
			adds = append(adds, new)
		}
	}
	for key, _ := range oldMap {
		dels = append(dels, key)
	}
	return adds, dels
}

// EditMiruCameraGroup 请求参数
type EditMiruCameraGroupReq struct {
	pub.H5ReqHeader
	GroupID     string   `json:"group_id"`     // 设备组ID
	Name        string   `json:"name"`         // 设备组名称
	BindAccount []string `json:"bind_account"` // 授权用户列表
}

// EditMiruCameraGroup 编辑设备组
func EditMiruCameraGroup(c *gin.Context) {

	uuid := c.GetString(pub.LOGID)
	account := c.GetString(pub.H5ACCOUNT)
	var req EditMiruCameraGroupReq
	err := c.BindJSON(&req)
	if err != nil {
		logger.Info("json unmarshal error: %s, uuid: %s", err, uuid)
		pub.SendNormal(c, pub.ErrInvalidBody, uuid)
		return
	}
	// 校验必要参数
	if req.GroupID == "" || req.Name == "" {
		pub.SendNormal(c, pub.ErrInvalidParam, uuid)
		return
	}
	if !mirulib.DB.Query(mirulib.GCameraGroup).Filter("id", req.GroupID).Exist() {
		pub.SendNormal(c, pub.ErrInvalidParam, uuid)
		return
	}
	qs := mirulib.DB.Query(mirulib.GCameraGroup).Filter("name", req.Name)
	if qs.Exist() {
		var dbCGObj mirulib.TBCameraGroup
		qs.First(&dbCGObj)
		if dbCGObj.ID != req.GroupID {
			pub.SendNormal(c, pub.ErrUserNameUsed, uuid)
			return
		}
	}
	mirulib.DB.Query(mirulib.GCameraGroup).Filter("id", req.GroupID).Update("name", req.Name)
	var dbPUGObjs []mirulib.TBPhoneUserGroup
	mirulib.DB.Query(mirulib.GPhoneUserGroup).Filter("group_id", req.GroupID).All(&dbPUGObjs)
	var olds = []string{}
	for _, dbPUGObj := range dbPUGObjs {
		olds = append(olds, dbPUGObj.Account)
	}
	adds, dels := getIncrementItem(olds, req.BindAccount)
	for _, add := range adds {
		var dbPUGObj mirulib.TBPhoneUserGroup
		dbPUGObj.Account = add
		dbPUGObj.BindTime = time.Now().Unix()
		dbPUGObj.GroupID = req.GroupID
		mirulib.DB.Insert(&dbPUGObj)
	}
	for _, del := range dels {
		mirulib.DB.Query(mirulib.GPhoneUserGroup).Filter("group_id", req.GroupID, "account", del).DeleteEx()
	}

	var dbMOLObj mirulib.TBMiruOperateLog
	dbMOLObj.Account = account
	dbMOLObj.CreateTime = time.Now().Unix()
	contentMap := map[string]string{
		"group_name": req.Name,
	}
	bs, _ := json.Marshal(&contentMap)
	dbMOLObj.OperateContent = string(bs)
	dbMOLObj.OperateType = pub.MiruOperateEditGroup
	mirulib.DB.Insert(&dbMOLObj)

	pub.SendNormal(c, pub.StatusOk, uuid)
	return
}

// DelMiruCameraGroupReq 请求参数
type DelMiruCameraGroupReq struct {
	pub.H5ReqHeader
	GroupID string `json:"group_id"` // 设备组ID
}

// DelMiruCameraGroup 删除设备组
func DelMiruCameraGroup(c *gin.Context) {

	uuid := c.GetString(pub.LOGID)
	var req DelMiruCameraGroupReq
	err := c.BindJSON(&req)
	if err != nil {
		logger.Info("json unmarshal error: %s, uuid: %s", err, uuid)
		pub.SendNormal(c, pub.ErrInvalidBody, uuid)
		return
	}
	// 校验必要参数
	if req.GroupID == "" {
		pub.SendNormal(c, pub.ErrInvalidParam, uuid)
		return
	}
	// if mirulib.DB.Query(mirulib.GCamera).Filter("group_id", req.GroupID).Exist() {
	// 	pub.SendNormal(c, pub.ErrDeviceExistWithinGroup, uuid)
	// 	return
	// }

	mirulib.DB.Query(mirulib.GCameraGroup).Filter("id", req.GroupID).DeleteEx()
	mirulib.DB.Query(mirulib.GPhoneUserGroup).Filter("group_id", req.GroupID).DeleteEx()
	mirulib.DB.Query(mirulib.GCamera).Filter("group_id", req.GroupID).DeleteEx()

	pub.SendNormal(c, pub.StatusOk, uuid)
	return
}

// ListMiruCameraGroupReq 请求参数
type ListMiruCameraGroupReq struct {
	pub.H5ReqHeader
	Offset int `json:"offset"` // 偏移量
	Limit  int `json:"limit"`  // 返回数据条数
}

type ListMiruCameraGroupRsp struct {
	pub.RspHeader
	Count int64                     `json:"count"` // 符合条件的计数
	Datas []ListMiruCameraGroupData `json:"datas"` // 数据详情
}

type ListMiruCameraGroupData struct {
	GroupID string `json:"group_id"` // 设备组ID
	Name    string `json:"name"`     // 设备组名称
}

// ListMiruCameraGroup 设备组列表
func ListMiruCameraGroup(c *gin.Context) {

	uuid := c.GetString(pub.LOGID)
	account := c.GetString(pub.H5ACCOUNT)
	var req ListMiruCameraGroupReq
	err := c.BindJSON(&req)
	if err != nil {
		logger.Info("json unmarshal error: %s, uuid: %s", err, uuid)
		pub.SendNormal(c, pub.ErrInvalidBody, uuid)
		return
	}
	var limit = 20
	if req.Limit > 0 && req.Limit <= 1000 {
		limit = req.Limit
	}
	var dbPUObj mirulib.TBPhoneUser
	var count int64
	mirulib.DB.Query(mirulib.GPhoneUser).Filter("account", account).First(&dbPUObj)
	var dbCGObjs []mirulib.TBCameraGroup
	qs := mirulib.DB.Query(mirulib.GCameraGroup)
	if dbPUObj.AccountType == int16(pub.RoleTypeManager) {
		var dbPUGObjs []mirulib.TBPhoneUserGroup
		mirulib.DB.Query(mirulib.GPhoneUserGroup).Filter("account", account).All(&dbPUGObjs)
		var groupIDs = []string{}
		for _, dbPUGObj := range dbPUGObjs {
			groupIDs = append(groupIDs, dbPUGObj.GroupID)
		}
		if len(groupIDs) == 0 {
			count = 0
		} else {
			qs.Filter("id__in", groupIDs)
			count, _ = qs.Count()
			qs.Limit(limit, req.Offset).OrderBy("name").All(&dbCGObjs)
		}
	} else {
		count, _ = qs.Count()
		qs.Limit(limit, req.Offset).OrderBy("name").All(&dbCGObjs)
	}
	var datas = []ListMiruCameraGroupData{}
	for _, dbCGObj := range dbCGObjs {
		var data ListMiruCameraGroupData
		data.GroupID = dbCGObj.ID
		data.Name = dbCGObj.Name
		datas = append(datas, data)
	}
	var rsp ListMiruCameraGroupRsp
	rsp.RspHeader = pub.GetCodeJson(pub.StatusOk, uuid)
	rsp.Count = count
	rsp.Datas = datas
	pub.SendJSON(c, rsp)
	return
}

// ListMiruCameraGroupReq 请求参数
type InfoMiruCameraGroupReq struct {
	pub.H5ReqHeader
	GroupID string `json:"group_id"` // 设备组ID
}

type InfoMiruCameraGroupRsp struct {
	pub.RspHeader
	Data InfoMiruCameraGroupData `json:"data"` // 数据详情
}

type InfoMiruCameraGroupData struct {
	GroupID     string   `json:"group_id"`     // 设备组ID
	Name        string   `json:"name"`         // 设备组名称
	BindAccount []string `json:"bind_account"` // 授权用户列表
}

// InfoMiruCameraGroup 设备组详情
func InfoMiruCameraGroup(c *gin.Context) {

	uuid := c.GetString(pub.LOGID)
	var req InfoMiruCameraGroupReq
	err := c.BindJSON(&req)
	if err != nil {
		logger.Info("json unmarshal error: %s, uuid: %s", err, uuid)
		pub.SendNormal(c, pub.ErrInvalidBody, uuid)
		return
	}
	// 校验必要参数
	if req.GroupID == "" {
		pub.SendNormal(c, pub.ErrInvalidParam, uuid)
		return
	}
	var dbCGObj mirulib.TBCameraGroup
	mirulib.DB.Query(mirulib.GCameraGroup).Filter("id", req.GroupID).First(&dbCGObj)
	var dbPUGObjs []mirulib.TBPhoneUserGroup
	mirulib.DB.Query(mirulib.GPhoneUserGroup).Filter("group_id", req.GroupID).All(&dbPUGObjs)
	var data InfoMiruCameraGroupData
	var accounts = []string{}
	for _, dbPUGObj := range dbPUGObjs {
		accounts = append(accounts, dbPUGObj.Account)
	}
	data.BindAccount = accounts
	data.GroupID = dbCGObj.ID
	data.Name = dbCGObj.Name
	var rsp InfoMiruCameraGroupRsp
	rsp.RspHeader = pub.GetCodeJson(pub.StatusOk, uuid)
	rsp.Data = data
	pub.SendJSON(c, rsp)
	return
}

// ListMiruCameraReq 请求参数
type ListMiruCameraReq struct {
	pub.H5ReqHeader
	Text    string `json:"text"`     // 搜索文本
	GroupID string `json:"group_id"` // 设备组ID
	Offset  int    `json:"offset"`   // 偏移量
	Limit   int    `json:"limit"`    // 返回数据条数
}

type ListMiruCameraRsp struct {
	pub.RspHeader
	Count int64                `json:"count"` // 符合条件的计数
	Datas []ListMiruCameraData `json:"datas"` // 数据详情
}

type ListMiruCameraData struct {
	Cid       string `json:"cid"`        // 设备CID
	Net       int    `json:"net"`        // 网络状态 0-离线 大于0-在线
	Version   string `json:"version"`    // 版本号
	Name      string `json:"name"`       // 设备昵称
	GroupName string `json:"group_name"` // 设备分组
}

// ListMiruCamera 查询设备列表
func ListMiruCamera(c *gin.Context) {

	uuid := c.GetString(pub.LOGID)
	account := c.GetString(pub.H5ACCOUNT)
	var req ListMiruCameraReq
	err := c.BindJSON(&req)
	if err != nil {
		logger.Info("json unmarshal error: %s, uuid: %s", err, uuid)
		pub.SendNormal(c, pub.ErrInvalidBody, uuid)
		return
	}
	var limit = 20
	if req.Limit > 0 && req.Limit <= 1000 {
		limit = req.Limit
	}
	var dbPUObj mirulib.TBPhoneUser
	mirulib.DB.Query(mirulib.GPhoneUser).Filter("account", account).First(&dbPUObj)
	var input pub.QueryCameraInput
	input.Account = account
	input.GroupID = req.GroupID
	input.Limit = limit
	input.Offset = req.Offset
	input.Text = req.Text
	dbCObjs, count := pub.QueryCameraInfo(input)
	var datas = []ListMiruCameraData{}
	var groupNameMap = make(map[string]string)
	for _, dbCObj := range dbCObjs {
		var data ListMiruCameraData
		data.Name = dbCObj.Name
		data.Cid = dbCObj.Cid
		data.Version = dbCObj.Version
		data.Net = dbCObj.Net
		_, ok := groupNameMap[dbCObj.GroupID]
		if !ok {
			var dbCGObj mirulib.TBCameraGroup
			mirulib.DB.Query(mirulib.GCameraGroup).Filter("id", dbCObj.GroupID).First(&dbCGObj)
			groupNameMap[dbCObj.GroupID] = dbCGObj.Name
		}
		data.GroupName = groupNameMap[dbCObj.GroupID]
		datas = append(datas, data)
	}
	var rsp ListMiruCameraRsp
	rsp.RspHeader = pub.GetCodeJson(pub.StatusOk, uuid)
	rsp.Count = count
	rsp.Datas = datas
	pub.SendJSON(c, rsp)
	return
}

// ListMiruCameraReq 请求参数
type EditMiruCameraReq struct {
	pub.H5ReqHeader
	Cid  string `json:"cid"`  // 设备CID
	Name string `json:"name"` // 设备昵称
}

// EditMiruCamera 编辑设备昵称
func EditMiruCamera(c *gin.Context) {

	uuid := c.GetString(pub.LOGID)
	var req EditMiruCameraReq
	err := c.BindJSON(&req)
	if err != nil {
		logger.Info("json unmarshal error: %s, uuid: %s", err, uuid)
		pub.SendNormal(c, pub.ErrInvalidBody, uuid)
		return
	}
	// 校验必要参数
	if req.Cid == "" || req.Name == "" {
		pub.SendNormal(c, pub.ErrInvalidParam, uuid)
		return
	}
	kvsCamObj := mirulib.GetKVSCam(req.Cid)
	kvsCamObj.SetDebugStr(uuid)
	kvsCamObj.Get()
	kvsCamObj.Name = req.Name
	kvsCamObj.Save(false)
	pub.SendNormal(c, pub.StatusOk, uuid)
	return
}

// DelMiruCameraReq 请求参数
type DelMiruCameraReq struct {
	pub.H5ReqHeader
	Cid string `json:"cid"` // 设备CID
}

// DelMiruCamera 删除设备
func DelMiruCamera(c *gin.Context) {

	uuid := c.GetString(pub.LOGID)
	var req DelMiruCameraReq
	err := c.BindJSON(&req)
	if err != nil {
		logger.Info("json unmarshal error: %s, uuid: %s", err, uuid)
		pub.SendNormal(c, pub.ErrInvalidBody, uuid)
		return
	}
	// 校验必要参数
	if req.Cid == "" {
		pub.SendNormal(c, pub.ErrInvalidParam, uuid)
		return
	}
	kvsCamObj := mirulib.GetKVSCam(req.Cid)
	kvsCamObj.SetDebugStr(uuid)
	kvsCamObj.Get()
	kvsCamObj.Delete(true)
	pub.SendNormal(c, pub.StatusOk, uuid)
	return
}

// ListMiruAccountReq 请求参数
type ListMiruAccountReq struct {
	pub.H5ReqHeader
	Offset int `json:"offset"` // 偏移量
	Limit  int `json:"limit"`  // 返回数据条数
}

type ListMiruAccountRsp struct {
	pub.RspHeader
	Count int64                 `json:"count"` // 符合条件的计数
	Datas []ListMiruAccountData `json:"datas"` // 数据详情
}

type ListMiruAccountData struct {
	Account     string `json:"account"`      // 账号
	Name        string `json:"name"`         // 昵称
	Remark      string `json:"remark"`       // 备注
	AccountType int16  `json:"account_type"` // 账号类型，1-超管 3-管理员 4-访客
}

// ListMiruAccount 查询用户列表
func ListMiruAccount(c *gin.Context) {

	uuid := c.GetString(pub.LOGID)
	var req ListMiruAccountReq
	err := c.BindJSON(&req)
	if err != nil {
		logger.Info("json unmarshal error: %s, uuid: %s", err, uuid)
		pub.SendNormal(c, pub.ErrInvalidBody, uuid)
		return
	}
	var limit = 20
	if req.Limit > 0 && req.Limit <= 1000 {
		limit = req.Limit
	}
	var dbPUObjs []mirulib.TBPhoneUser
	qs := mirulib.DB.Query(mirulib.GPhoneUser).Exclude("account_type", pub.RoleTypeSuper)
	count, _ := qs.Count()
	qs.Limit(limit, req.Offset).OrderBy("-register_time").All(&dbPUObjs)
	var datas = []ListMiruAccountData{}
	for _, dbPUObj := range dbPUObjs {
		var data ListMiruAccountData
		data.Name = dbPUObj.Alias
		data.Account = dbPUObj.Account
		data.Remark = dbPUObj.Remark
		data.AccountType = dbPUObj.AccountType
		datas = append(datas, data)
	}
	var rsp ListMiruAccountRsp
	rsp.RspHeader = pub.GetCodeJson(pub.StatusOk, uuid)
	rsp.Count = count
	rsp.Datas = datas
	pub.SendJSON(c, rsp)
	return
}

// AddMiruAccountReq 请求参数
type AddMiruAccountReq struct {
	pub.H5ReqHeader
	Account     string `json:"account"`      // 账号
	Password    string `json:"password"`     // 密码，MD5加密后的密文
	Name        string `json:"name"`         // 昵称
	Remark      string `json:"remark"`       // 备注
	AccountType int16  `json:"account_type"` // 账号类型，1-超管 3-管理员 4-访客
}

// AddMiruAccount 新增用户
func AddMiruAccount(c *gin.Context) {

	uuid := c.GetString(pub.LOGID)
	account := c.GetString(pub.H5ACCOUNT)
	var req AddMiruAccountReq
	err := c.BindJSON(&req)
	if err != nil {
		logger.Info("json unmarshal error: %s, uuid: %s", err, uuid)
		pub.SendNormal(c, pub.ErrInvalidBody, uuid)
		return
	}
	// 校验必要参数
	if req.Account == "" || req.Password == "" {
		pub.SendNormal(c, pub.ErrInvalidParam, uuid)
		return
	}
	count, _ := mirulib.DB.Query(mirulib.GPhoneUser).Count()
	if count >= 6 { // 限制新建5个账号
		pub.SendNormal(c, pub.ErrAccountNumberExceeds, uuid)
		return
	}
	if mirulib.DB.Query(mirulib.GPhoneUser).Filter("account", req.Account).Exist() {
		logger.Info("account: %s is exist, uuid: %s", req.Account, uuid)
		pub.SendNormal(c, pub.ErrAccountExist, uuid)
		return
	}

	var dbPUObj mirulib.TBPhoneUser
	dbPUObj.Account = req.Account
	dbPUObj.Alias = req.Name
	dbPUObj.AccountType = req.AccountType
	dbPUObj.Password = req.Password
	dbPUObj.Remark = req.Remark
	dbPUObj.RegisterTime = time.Now().Unix()
	mirulib.DB.Insert(&dbPUObj)

	var dbMOLObj mirulib.TBMiruOperateLog
	dbMOLObj.Account = account
	dbMOLObj.CreateTime = time.Now().Unix()
	contentMap := map[string]string{
		"account": req.Account,
	}
	bs, _ := json.Marshal(&contentMap)
	dbMOLObj.OperateContent = string(bs)
	dbMOLObj.OperateType = pub.MiruOperateAddAccount
	mirulib.DB.Insert(&dbMOLObj)

	pub.SendNormal(c, pub.StatusOk, uuid)
}

// EditMiruAccountReq 请求参数
type EditMiruAccountReq struct {
	pub.H5ReqHeader
	Account string `json:"account"` // 账号
	Name    string `json:"name"`    // 昵称
	Remark  string `json:"remark"`  // 备注
}

// EditMiruAccount 编辑用户
func EditMiruAccount(c *gin.Context) {

	uuid := c.GetString(pub.LOGID)
	account := c.GetString(pub.H5ACCOUNT)
	var req EditMiruAccountReq
	err := c.BindJSON(&req)
	if err != nil {
		logger.Info("json unmarshal error: %s, uuid: %s", err, uuid)
		pub.SendNormal(c, pub.ErrInvalidBody, uuid)
		return
	}
	// 校验必要参数
	if req.Account == "" {
		pub.SendNormal(c, pub.ErrInvalidParam, uuid)
		return
	}

	mirulib.DB.Query(mirulib.GPhoneUser).Filter("account", req.Account).Update("alias", req.Name, "remark", req.Remark)
	kvsPUObj := mirulib.GetKVSPU(req.Account)
	kvsPUObj.Get()
	kvsPUObj.Delete(false)

	var dbMOLObj mirulib.TBMiruOperateLog
	dbMOLObj.Account = account
	dbMOLObj.CreateTime = time.Now().Unix()
	contentMap := map[string]string{
		"account": req.Account,
	}
	bs, _ := json.Marshal(&contentMap)
	dbMOLObj.OperateContent = string(bs)
	dbMOLObj.OperateType = pub.MiruOperateEditAccount
	mirulib.DB.Insert(&dbMOLObj)

	pub.SendNormal(c, pub.StatusOk, uuid)
}

// DelMiruAccountReq 请求参数
type DelMiruAccountReq struct {
	pub.H5ReqHeader
	Account string `json:"account"` // 账号，多个用逗号(,)隔开
}

// DelMiruAccount 删除用户
func DelMiruAccount(c *gin.Context) {

	uuid := c.GetString(pub.LOGID)
	account := c.GetString(pub.H5ACCOUNT)
	var req DelMiruAccountReq
	err := c.BindJSON(&req)
	if err != nil {
		logger.Info("json unmarshal error: %s, uuid: %s", err, uuid)
		pub.SendNormal(c, pub.ErrInvalidBody, uuid)
		return
	}
	// 校验必要参数
	if req.Account == "" {
		pub.SendNormal(c, pub.ErrInvalidParam, uuid)
		return
	}
	accounts := strings.Split(req.Account, ",")
	for _, account := range accounts {
		//mirulib.DB.Query(mirulib.GPhoneUser).Filter("account", account).DeleteEx()
		kvsPUObj := mirulib.GetKVSPU(account)
		kvsPUObj.Get()
		kvsPUObj.Delete(true)
	}

	var dbMOLObj mirulib.TBMiruOperateLog
	dbMOLObj.Account = account
	dbMOLObj.CreateTime = time.Now().Unix()
	contentMap := map[string]string{
		"account": req.Account,
	}
	bs, _ := json.Marshal(&contentMap)
	dbMOLObj.OperateContent = string(bs)
	dbMOLObj.OperateType = pub.MiruOperateDelAccount
	mirulib.DB.Insert(&dbMOLObj)

	pub.SendNormal(c, pub.StatusOk, uuid)
}

// MiruAccountResetPasswordReq 请求参数
type MiruAccountResetPasswordReq struct {
	pub.H5ReqHeader
	Account string `json:"account"` // 账号
}

// MiruAccountResetPassword 重置用户密码
func MiruAccountResetPassword(c *gin.Context) {

	uuid := c.GetString(pub.LOGID)
	account := c.GetString(pub.H5ACCOUNT)
	var req MiruAccountResetPasswordReq
	err := c.BindJSON(&req)
	if err != nil {
		logger.Info("json unmarshal error: %s, uuid: %s", err, uuid)
		pub.SendNormal(c, pub.ErrInvalidBody, uuid)
		return
	}
	// 校验必要参数
	if req.Account == "" {
		pub.SendNormal(c, pub.ErrInvalidParam, uuid)
		return
	}

	mirulib.DB.Query(mirulib.GPhoneUser).Filter("account", req.Account).Update("password", pub.GetMd5String("123456"))

	kvsPUObj := mirulib.GetKVSPU(req.Account)
	kvsPUObj.Get()
	kvsPUObj.Delete(false)

	var dbMOLObj mirulib.TBMiruOperateLog
	dbMOLObj.Account = account
	dbMOLObj.CreateTime = time.Now().Unix()
	contentMap := map[string]string{
		"account": req.Account,
	}
	bs, _ := json.Marshal(&contentMap)
	dbMOLObj.OperateContent = string(bs)
	dbMOLObj.OperateType = pub.MiruOperateResetPassword
	mirulib.DB.Insert(&dbMOLObj)

	pub.SendNormal(c, pub.StatusOk, uuid)
}

type MiruAccountChangePasswordReq struct {
	pub.H5ReqHeader
	PasswordOld string `json:"password_old"` // 旧密码
	PasswordNew string `json:"password_new"` // 新密码
}

// MiruAccountResetPassword 修改密码
func MiruAccountChangePassword(c *gin.Context) {

	uuid := c.GetString(pub.LOGID)
	account := c.GetString(pub.H5ACCOUNT)
	var req MiruAccountChangePasswordReq
	err := c.BindJSON(&req)
	if err != nil {
		logger.Info("json unmarshal error: %s, uuid: %s", err, uuid)
		pub.SendNormal(c, pub.ErrInvalidBody, uuid)
		return
	}
	// 校验必要参数
	if req.PasswordOld == "" || req.PasswordNew == "" {
		pub.SendNormal(c, pub.ErrInvalidParam, uuid)
		return
	}

	var dbPUObj mirulib.TBPhoneUser
	mirulib.DB.Query(mirulib.GPhoneUser).Filter("account", account).First(&dbPUObj)
	if dbPUObj.Password != req.PasswordOld {
		pub.SendNormal(c, pub.ErrWrongPassword, uuid)
		return
	}
	mirulib.DB.Query(mirulib.GPhoneUser).Filter("account", account).Update("password", req.PasswordNew)

	kvsPUObj := mirulib.GetKVSPU(account)
	kvsPUObj.Get()
	kvsPUObj.Delete(false)

	var dbMOLObj mirulib.TBMiruOperateLog
	dbMOLObj.Account = account
	dbMOLObj.CreateTime = time.Now().Unix()
	contentMap := map[string]string{
		"account": account,
		"alias":   dbPUObj.Alias,
	}
	bs, _ := json.Marshal(&contentMap)
	dbMOLObj.OperateContent = string(bs)
	dbMOLObj.OperateType = pub.MiruOperateChangePassword
	mirulib.DB.Insert(&dbMOLObj)

	pub.SendNormal(c, pub.StatusOk, uuid)
}

type MiruAccountRenameReq struct {
	pub.H5ReqHeader
	Name string `json:"name"` // 名称
}

// MiruAccountRename 修改昵称
func MiruAccountRename(c *gin.Context) {

	uuid := c.GetString(pub.LOGID)
	account := c.GetString(pub.H5ACCOUNT)
	var req MiruAccountRenameReq
	err := c.BindJSON(&req)
	if err != nil {
		logger.Info("json unmarshal error: %s, uuid: %s", err, uuid)
		pub.SendNormal(c, pub.ErrInvalidBody, uuid)
		return
	}

	mirulib.DB.Query(mirulib.GPhoneUser).Filter("account", account).Update("alias", req.Name)

	kvsPUObj := mirulib.GetKVSPU(account)
	kvsPUObj.Get()
	kvsPUObj.Delete(false)

	pub.SendNormal(c, pub.StatusOk, uuid)
}

// ListMiruOperateLogReq 请求参数
type ListMiruOperateLogReq struct {
	pub.H5ReqHeader
	BeginTime   int64 `json:"begin_time"`   // 查询起始时间，单位：秒
	EndTime     int64 `json:"end_time"`     // 查询结束时间，单位：秒
	OperateType int   `json:"operate_type"` // 操作类型 0-全部 1-登录 2-编辑设备组 3-添加用户 4-编辑用户 5-删除用户 6-重置用户密码 7-修改密码
	Offset      int   `json:"offset"`       // 偏移量
	Limit       int   `json:"limit"`        // 返回数据条数
}

type ListMiruOperateLogRsp struct {
	pub.RspHeader
	Count int64                    `json:"count"` // 符合条件的计数
	Datas []ListMiruOperateLogData `json:"datas"` // 数据详情
}

type ListMiruOperateLogData struct {
	Time           int64  `json:"time"`            // 操作时间
	Account        string `json:"account"`         // 账号
	OperateType    int    `json:"operate_type"`    // 操作类型 1-登录 2-编辑设备组 3-添加用户 4-编辑用户 5-删除用户 6-重置用户密码 7-修改密码
	OperateContent string `json:"operate_content"` // 操作内容
}

// ListMiruOperateLog 操作日志列表
func ListMiruOperateLog(c *gin.Context) {

	uuid := c.GetString(pub.LOGID)
	var req ListMiruOperateLogReq
	err := c.BindJSON(&req)
	if err != nil {
		logger.Info("json unmarshal error: %s, uuid: %s", err, uuid)
		pub.SendNormal(c, pub.ErrInvalidBody, uuid)
		return
	}
	var limit = 20
	if req.Limit > 0 && req.Limit <= 1000 {
		limit = req.Limit
	}
	var dbMOLObjs []mirulib.TBMiruOperateLog
	qs := mirulib.DB.Query(mirulib.GMiruOperateLog)
	if req.BeginTime > 0 {
		qs.Filter("create_time__gte", req.BeginTime)
	}
	if req.EndTime > 0 {
		qs.Filter("create_time__lte", req.EndTime)
	}
	if req.OperateType != 0 {
		qs.Filter("operate_type", req.OperateType)
	}
	count, _ := qs.Count()
	qs.Limit(limit, req.Offset).OrderBy("-create_time").All(&dbMOLObjs)
	var datas = []ListMiruOperateLogData{}
	for _, dbMOLObj := range dbMOLObjs {
		var data ListMiruOperateLogData
		data.Account = dbMOLObj.Account
		data.OperateContent = dbMOLObj.OperateContent
		data.OperateType = dbMOLObj.OperateType
		data.Time = dbMOLObj.CreateTime
		datas = append(datas, data)
	}
	var rsp ListMiruOperateLogRsp
	rsp.RspHeader = pub.GetCodeJson(pub.StatusOk, uuid)
	rsp.Count = count
	rsp.Datas = datas
	pub.SendJSON(c, rsp)
	return
}

// GetMiruCameraVideoReq 请求参数
type GetMiruCameraVideoReq struct {
	pub.H5ReqHeader
	BeginTime int64  `json:"begin_time"` // 起始时间，单位：秒
	EndTime   int64  `json:"end_time"`   // 结束时间，单位：秒
	Enable    bool   `json:"enable"`     // true-上传视频 false-停止上传
	Cid       string `json:"cid"`        // 设备CID
}

// GetMiruCameraVideo 获取视频
func GetMiruCameraVideo(c *gin.Context) {

	uuid := c.GetString(pub.LOGID)
	var req GetMiruCameraVideoReq
	err := c.BindJSON(&req)
	if err != nil {
		logger.Info("json unmarshal error: %s, uuid: %s", err, uuid)
		pub.SendNormal(c, pub.ErrInvalidBody, uuid)
		return
	}
	if req.Cid == "" {
		pub.SendNormal(c, pub.ErrInvalidParam, uuid)
		return
	}
	count, _ := mirulib.DB.Query(mirulib.GCameraVideo).Count()

	var need = (req.EndTime - req.BeginTime) / 60
	if count+need > 100 {
		pub.SendNormal(c, pub.ErrorVideoExceeds, uuid)
		return
	}
	// todo 给设备发送获取视频DP
	pushMsg := worker.JPubDpSet{}
	pushMsg.JsonHeader = worker.GetJsonHeader(worker.JIDPubDpPushData, uuid, "server", req.Cid)
	var dpData mirulib.JDPData
	dpData.ID = worker.DPIDCameraUploadVideo
	dpData.Time = time.Now().Unix()
	var bd worker.DPCameraUploadVideoMsg
	bd.BeginTime = req.BeginTime
	bd.EndTime = req.EndTime
	bd.Enable = req.Enable
	dpData.Value = worker.JsonMarshal(bd)
	pushMsg.Body = []mirulib.JDPData{dpData}

	worker.SendWorkMsgBySessid(req.Cid, pushMsg)
	pub.SendNormal(c, pub.StatusOk, uuid)
	return
}

// ListMiruCameraVideoReq 请求参数
type ListMiruCameraVideoReq struct {
	pub.H5ReqHeader
	GroupID   string `json:"group_id"`   // 分组ID，查询全部为空
	Text      string `json:"text"`       // 设备CID/设备昵称
	BeginTime int64  `json:"begin_time"` // 查询起始时间，单位：秒
	EndTime   int64  `json:"end_time"`   // 查询结束时间，单位：秒
	Offset    int    `json:"offset"`     // 偏移量
	Limit     int    `json:"limit"`      // 返回数据条数
}

type ListMiruCameraVideoRsp struct {
	pub.RspHeader
	Count int64                     `json:"count"` // 符合条件的计数
	Datas []ListMiruCameraVideoData `json:"datas"` // 数据详情
}

type ListMiruCameraVideoData struct {
	VideoID    int    `json:"video_id"`    // 视频ID
	GroupName  string `json:"group_name"`  // 分组名称
	CameraName string `json:"camera_name"` // 设备昵称
	BeginTime  int64  `json:"begin_time"`  // 视频开始时间，单位：秒
	EndTime    int64  `json:"end_time"`    // 视频结束时间，单位：秒
	FileURL    string `json:"file_url"`    // 视频文件地址
	ImageURL   string `json:"image_url"`   // 封面文件地址
}

// ListMiruCameraVideo 视频列表
func ListMiruCameraVideo(c *gin.Context) {

	uuid := c.GetString(pub.LOGID)
	var req ListMiruCameraVideoReq
	err := c.BindJSON(&req)
	if err != nil {
		logger.Info("json unmarshal error: %s, uuid: %s", err, uuid)
		pub.SendNormal(c, pub.ErrInvalidBody, uuid)
		return
	}
	var limit = 20
	if req.Limit > 0 && req.Limit <= 1000 {
		limit = req.Limit
	}
	var input pub.QueryCameraVideoInput
	input.BeginTime = req.BeginTime
	input.EndTime = req.EndTime
	input.GroupID = req.GroupID
	input.Limit = limit
	input.Offset = req.Offset
	input.Text = req.Text
	dbCVObjs, count := pub.QueryCameraVideo(input)

	var datas = []ListMiruCameraVideoData{}
	var groupNames = make(map[string]string)
	for _, dbCVObj := range dbCVObjs {
		var data ListMiruCameraVideoData
		data.BeginTime = dbCVObj.BeginTime
		data.CameraName = dbCVObj.Name
		data.EndTime = dbCVObj.EndTime
		data.FileURL = conf.App.WebServer.VideoUrl + "/" + dbCVObj.FileURL
		data.ImageURL = strings.Replace(data.FileURL, ".mp4", ".jpg", -1)
		data.VideoID = dbCVObj.VideoID
		_, ok := groupNames[dbCVObj.GroupID]
		if !ok {
			var dbCGObj mirulib.TBCameraGroup
			mirulib.DB.Query(mirulib.GCameraGroup).Filter("id", dbCVObj.GroupID).First(&dbCGObj)
			groupNames[dbCVObj.GroupID] = dbCGObj.Name
		}
		data.GroupName = groupNames[dbCVObj.GroupID]
		datas = append(datas, data)
	}
	var rsp ListMiruCameraVideoRsp
	rsp.RspHeader = pub.GetCodeJson(pub.StatusOk, uuid)
	rsp.Count = count
	rsp.Datas = datas
	pub.SendJSON(c, rsp)
	return
}

// DelMiruCameraVideoReq 请求参数
type DelMiruCameraVideoReq struct {
	pub.H5ReqHeader
	VideoID []int `json:"video_id"` // 视频ID
}

// DelMiruCameraVideo 获取视频
func DelMiruCameraVideo(c *gin.Context) {

	uuid := c.GetString(pub.LOGID)
	var req DelMiruCameraVideoReq
	err := c.BindJSON(&req)
	if err != nil {
		logger.Info("json unmarshal error: %s, uuid: %s", err, uuid)
		pub.SendNormal(c, pub.ErrInvalidBody, uuid)
		return
	}
	for _, videoID := range req.VideoID {
		var dbObj mirulib.TBCameraVideo
		mirulib.DB.Query(mirulib.GCameraVideo).Filter("video_id", videoID).First(&dbObj)
		if dbObj.FileURL != "" {
			os.Remove(filepath.Join(videoDir, dbObj.FileURL))
			os.Remove(filepath.Join(videoDir, strings.Replace(dbObj.FileURL, ".mp4", ".jpg", -1)))
		}
		mirulib.DB.Query(mirulib.GCameraVideo).Filter("video_id", videoID).DeleteEx()
	}
	pub.SendNormal(c, pub.StatusOk, uuid)
	return
}

// UploadFormFile multipart/form-data表单上传文件
func UploadFormFile(c *gin.Context) {

	uuid := pub.GetGuid()
	logger.Info("recv msg From: %s, uri: %s, UUID: %s", c.ClientIP(), c.Request.RequestURI, uuid)
	c.Request.ParseMultipartForm(256 << 20) //最多256M
	f, _, err := c.Request.FormFile("file")
	if err != nil {
		logger.Info("get file error: %s, UUID: %s", err, uuid)
		pub.SendNormal(c, http.StatusBadRequest, uuid)
		return
	}
	defer f.Close()
	videoFormat := c.PostForm("video_format") // 视频格式，服务端作文件名后缀用。如：mp4,avi等
	if videoFormat == "" {
		logger.Info("videoFormat is nil, UUID: %s", uuid)
		pub.SendNormal(c, pub.ErrInvalidParam, uuid)
		return
	}
	beginTimeStr := c.PostForm("begin_time") // 视频开始时间
	if beginTimeStr == "" {
		logger.Info("beginTime is nil, UUID: %s", uuid)
		pub.SendNormal(c, pub.ErrInvalidParam, uuid)
		return
	}
	beginTime, err := strconv.ParseInt(beginTimeStr, 10, 64)
	if err != nil {
		logger.Info("beginTime: %s ParseInt error: %s, UUID: %s", beginTimeStr, err, uuid)
		pub.SendNormal(c, pub.ErrInvalidParam, uuid)
		return
	}
	endTimeStr := c.PostForm("end_time") // 视频结束时间
	if endTimeStr == "" {
		logger.Info("endTime is nil, UUID: %s", uuid)
		pub.SendNormal(c, pub.ErrInvalidParam, uuid)
		return
	}
	endTime, err := strconv.ParseInt(endTimeStr, 10, 64)
	if err != nil {
		logger.Info("endTime: %s ParseInt error: %s, UUID: %s", endTimeStr, err, uuid)
		pub.SendNormal(c, pub.ErrInvalidParam, uuid)
		return
	}
	cid := c.PostForm("cid") // 设备CID
	if cid == "" {
		logger.Info("cid is nil, UUID: %s", uuid)
		pub.SendNormal(c, pub.ErrInvalidParam, uuid)
		return
	}

	kvsCamObj := mirulib.GetKVSCam(cid)
	kvsCamObj.SetDebugStr(uuid)
	kvsCamObj.Get()
	deviceName := kvsCamObj.Name

	os.MkdirAll(videoDir, os.ModePerm)
	filename := fmt.Sprintf("%s_%s.%s", deviceName, time.Unix(beginTime, 0).Format("20060102_150405"), videoFormat)
	filePath := filepath.Join(videoDir, filename)
	out, err := os.Create(filePath)
	if err != nil {
		logger.Info("Create file: %s error: %s, UUID: %s", filePath, err, uuid)
		pub.SendNormal(c, pub.ErrInternalServer, uuid)
		return
	}
	defer out.Close()
	if _, err := io.Copy(out, f); err != nil {
		logger.Info("Copy file: %s error: %s, UUID: %s", filename, err, uuid)
		pub.SendNormal(c, pub.ErrInternalServer, uuid)
		return
	}

	if videoFormat == "mp4" {
		var inputPath string = filePath
		var outputPath string = strings.Replace(filePath, ".mp4", ".jpg", -1)
		cmd := exec.Command(
			"ffmpeg",
			"-i", inputPath, // 输入文件
			"-ss", "00:00:00", // 定位到起始位置
			"-vframes", "1", // 只取一帧
			"-q:v", "1", // 输出图片质量，2表示高质量（范围1-31，1为最高质量）
			outputPath, // 输出文件
		)
		output, err := cmd.CombinedOutput()
		if err != nil {
			logger.Info("ffmpeg cmd exec error: %s -- info: %s", err, string(output))
		}
		var dbObj mirulib.TBCameraVideo
		dbObj.BeginTime = beginTime
		dbObj.Cid = cid
		dbObj.CreateTime = time.Now().Unix()
		dbObj.EndTime = endTime
		dbObj.FileURL = filename
		var dbCObj mirulib.TBCamera
		mirulib.DB.Query(mirulib.GCamera).Filter("cid", cid).First(&dbCObj)
		dbObj.GroupID = dbCObj.GroupID
		mirulib.DB.Insert(&dbObj)
	}

	pub.SendNormal(c, pub.StatusOk, uuid)
	return
}
